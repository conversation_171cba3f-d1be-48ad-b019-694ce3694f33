<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>台风专题大屏监控系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 使用使用BootCDN的Font Awesome Awesome链接 -->
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- 使用BootCDN的Chart.js链接 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/Chart.js/4.4.1/chart.umd.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#165DFF',
                        secondary: '#36BFFA',
                        warning: '#FF7D00',
                        danger: '#F53F3F',
                        dark: '#0D1117',
                        'dark-card': '#161B22',
                        'dark-border': '#30363D',
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .typhoon-active {
                @apply border-l-4 border-danger bg-dark-card/80;
            }
            .card-shadow {
                box-shadow: 0 0 15px rgba(22, 93, 255, 0.1);
            }
            .animate-pulse-slow {
                animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
            }
            .text-gradient {
                background-clip: text;
                -webkit-background-clip: text;
                color: transparent;
                background-image: linear-gradient(135deg, #165DFF, #36BFFA);
            }
        }
    </style>
</head>
<body class="bg-dark text-white min-h-screen overflow-x-hidden">
    <!-- 顶部导航栏 -->
    <header class="bg-dark-border/30 backdrop-blur-md border-b border-dark-border/50 sticky top-0 z-50">
        <div class="container mx-auto px-4 py-3">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                <div class="flex items-center space-x-2 w-full md:w-auto">
                    <i class="fa fa-tint text-primary text-2xl"></i>
                    <h1 class="text-xl md:text-2xl font-bold text-gradient">台风专题监控系统</h1>
                </div>
                
                <!-- 台风列表 -->
                <div class="w-full md:w-auto bg-dark-card/80 border border-dark-border/50 rounded-xl p-3 card-shadow w-full">
                    <div class="flex justify-between items-center mb-2">
                        <h2 class="text-sm md:text-base font-semibold flex items-center">
                            <i class="fa fa-list-ul text-primary mr-2"></i>活跃台风列表
                        </h2>
                        <span class="bg-danger/20 text-danger text-xs px-2 py-0.5 rounded-full">
                            <i class="fa fa-exclamation-circle mr-1"></i>
                            <span id="active-count">0</span>个活跃
                        </span>
                    </div>
                    <div id="typhoon-list" class="flex space-x-2 overflow-x-auto pb-2 min-w-full">
                        <!-- 台风列表将通过JavaScript动态生成 -->
                        <div class="flex items-center justify-center h-8 text-gray-400 flex-1">
                            <i class="fa fa-spinner fa-spin mr-2"></i>加载中...
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4 w-full md:w-auto justify-end">
                    <div class="hidden md:flex items-center space-x-2">
                        <i class="fa fa-clock-o text-secondary"></i>
                        <span id="current-time" class="text-sm md:text-base">加载中...</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i class="fa fa-refresh text-secondary"></i>
                        <span id="update-time" class="text-sm md:text-base">上次更新: 加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="container mx-auto px-4 py-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 左侧区域：台风详情和路径图 -->
            <div class="lg:col-span-2 space-y-6">
                <!-- 台风详情 -->
                <div id="typhoon-details" class="bg-dark-card border border-dark-border/50 rounded-xl p-4 card-shadow hidden">
                    <div class="flex justify-between items-center mb-6">
                        <div>
                            <h2 class="text-xl font-bold flex items-center">
                                <i class="fa fa-map-marker text-danger mr-2"></i>
                                <span id="selected-typhoon-name">台风名称</span>
                                <span class="ml-2 bg-danger/20 text-danger text-xs px-2 py-0.5 rounded-full">活跃中</span>
                            </h2>
                            <p class="text-gray-400 text-sm mt-1">台风编号: <span id="selected-typhoon-id">ID</span></p>
                        </div>
                        <div class="flex items-center space-x-3">
                            <button id="refresh-btn" class="bg-primary/20 hover:bg-primary/30 text-primary px-3 py-1.5 rounded-lg transition-all duration-300 flex items-center">
                                <i class="fa fa-refresh mr-1"></i>刷新
                            </button>
                        </div>
                    </div>

                    <!-- 台风数据指标 -->
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div class="bg-dark/50 rounded-lg p-4">
                            <div class="text-gray-400 text-sm mb-1">当前位置</div>
                            <div class="text-lg font-bold" id="current-location">--</div>
                        </div>
                        <div class="bg-dark/50 rounded-lg p-4">
                            <div class="text-gray-400 text-sm mb-1">台风等级</div>
                            <div class="text-lg font-bold" id="typhoon-type">--</div>
                        </div>
                        <div class="bg-dark/50 rounded-lg p-4">
                            <div class="text-gray-400 text-sm mb-1">中心气压</div>
                            <div class="text-lg font-bold" id="current-pressure">-- hPa</div>
                        </div>
                        <div class="bg-dark/50 rounded-lg p-4">
                            <div class="text-gray-400 text-sm mb-1">最大风速</div>
                            <div class="text-lg font-bold" id="current-wind-speed">-- m/s</div>
                        </div>
                    </div>

                    <!-- 台风未来趋势 -->
                    <div class="bg-dark/50 rounded-lg p-4">
                        <h3 class="text-base font-medium mb-3">未来趋势</h3>
                        <div class="grid grid-cols-1 gap-3">
                            <div class="bg-dark/70 rounded-lg p-2">
                                <div class="text-sm text-gray-400">12小时后</div>
                                <div class="text-sm">
                                    <span>位置: <span id="forecast-12h-location">--</span></span>
                                    <span class="ml-2">风速: <span id="forecast-12h-wind">--</span> m/s</span>
                                </div>
                            </div>
                            <div class="bg-dark/70 rounded-lg p-2">
                                <div class="text-sm text-gray-400">24小时后</div>
                                <div class="text-sm">
                                    <span>位置: <span id="forecast-24h-location">--</span></span>
                                    <span class="ml-2">风速: <span id="forecast-24h-wind">--</span> m/s</span>
                                </div>
                            </div>
                            <div class="bg-dark/70 rounded-lg p-2">
                                <div class="text-sm text-gray-400">48小时后</div>
                                <div class="text-sm">
                                    <span>位置: <span id="forecast-48h-location">--</span></span>
                                    <span class="ml-2">风速: <span id="forecast-48h-wind">--</span> m/s</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 台风路径图 -->
                <div class="bg-dark-card border border-dark-border/50 rounded-xl p-4 card-shadow">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-lg font-semibold flex items-center">
                            <i class="fa fa-map-o text-primary mr-2"></i>台风路径预测
                        </h2>
                        <button id="fullscreen-btn" class="bg-dark-border/50 hover:bg-dark-border/70 text-gray-300 px-3 py-1.5 rounded-lg transition-all duration-300 flex items-center">
                            <i class="fa fa-expand mr-1"></i>全屏查看
                        </button>
                    </div>
                    <div class="h-[400px] relative">
                        <canvas id="typhoon-path-chart"></canvas>
                        <div id="chart-loading" class="absolute inset-0 flex items-center justify-center bg-dark/70 rounded-lg">
                            <i class="fa fa-spinner fa-spin text-2xl text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧区域：设备数据卡片（独享一列） -->
            <div class="lg:col-span-1 space-y-6">
                <!-- 设备数据卡片 -->
                <div class="grid grid-cols-1 gap-4">
                    <div class="bg-dark-card border border-dark-border/50 rounded-xl p-4 card-shadow">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="font-medium flex items-center">
                                <i class="fa fa-thermometer-half text-warning mr-2"></i>温度监测
                            </h3>
                            <span class="text-xs text-gray-400">设备ID: TEMP-001</span>
                        </div>
                        <div class="flex items-end justify-between">
                            <div>
                                <div class="text-3xl font-bold" id="temperature-value">26.5°C</div>
                                <div class="text-xs text-gray-400 mt-1">较昨日 <span class="text-green-500">↓0.3°C</span></div>
                            </div>
                            <div class="w-20 h-20">
                                <canvas id="temperature-chart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="bg-dark-card border border-dark-border/50 rounded-xl p-4 card-shadow">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="font-medium flex items-center">
                                <i class="fa fa-tint text-secondary mr-2"></i>湿度监测
                            </h3>
                            <span class="text-xs text-gray-400">设备ID: HUM-001</span>
                        </div>
                        <div class="flex items-end justify-between">
                            <div>
                                <div class="text-3xl font-bold" id="humidity-value">78%</div>
                                <div class="text-xs text-gray-400 mt-1">较昨日 <span class="text-red-500">↑5%</span></div>
                            </div>
                            <div class="w-20 h-20">
                                <canvas id="humidity-chart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="bg-dark-card border border-dark-border/50 rounded-xl p-4 card-shadow">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="font-medium flex items-center">
                                <i class="fa fa-wind text-primary mr-2"></i>风速监测
                            </h3>
                            <span class="text-xs text-gray-400">设备ID: WIND-001</span>
                        </div>
                        <div class="flex items-end justify-between">
                            <div>
                                <div class="text-3xl font-bold" id="wind-speed-value">18.2 m/s</div>
                                <div class="text-xs text-gray-400 mt-1">风力等级: <span class="text-warning">7级</span></div>
                            </div>
                            <div class="w-20 h-20">
                                <canvas id="wind-speed-chart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="bg-dark-card border border-dark-border/50 rounded-xl p-4 card-shadow">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="font-medium flex items-center">
                                <i class="fa fa-compass text-purple-500 mr-2"></i>风向监测
                            </h3>
                            <span class="text-xs text-gray-400">设备ID: WDIR-001</span>
                        </div>
                        <div class="flex flex-col items-center">
                            <div class="w-24 h-24 relative mb-2">
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <i class="fa fa-compass text-4xl text-gray-500"></i>
                                </div>
                                <div id="wind-direction-indicator" class="absolute inset-0 flex items-center justify-center">
                                    <div class="w-2 h-12 bg-purple-500 rounded-t-full transform rotate-0 origin-bottom"></div>
                                </div>
                            </div>
                            <div class="text-xl font-bold" id="wind-direction-text">东北偏北</div>
                            <div class="text-xs text-gray-400">方位角: <span id="wind-direction-deg">45°</span></div>
                        </div>
                    </div>

                    <div class="bg-dark-card border border-dark-border/50 rounded-xl p-4 card-shadow">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="font-medium flex items-center">
                                <i class="fa fa-cloud text-gray-400 mr-2"></i>气压监测
                            </h3>
                            <span class="text-xs text-gray-400">设备ID: PRESS-001</span>
                        </div>
                        <div class="flex items-end justify-between">
                            <div>
                                <div class="text-3xl font-bold" id="pressure-value">1002 hPa</div>
                                <div class="text-xs text-gray-400 mt-1">趋势: <span class="text-red-500">↓0.5 hPa</span></div>
                            </div>
                            <div class="w-20 h-20">
                                <canvas id="pressure-chart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="bg-dark-card border border-dark-border/50 rounded-xl p-4 card-shadow">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="font-medium flex items-center">
                                <i class="fa fa-tint text-blue-400 mr-2"></i>降雨量监测
                            </h3>
                            <span class="text-xs text-gray-400">设备ID: RAIN-001</span>
                        </div>
                        <div class="flex items-end justify-between">
                            <div>
                                <div class="text-3xl font-bold" id="rainfall-value">25.8 mm</div>
                                <div class="text-xs text-gray-400 mt-1">24小时累计</div>
                            </div>
                            <div class="w-20 h-20">
                                <canvas id="rainfall-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark-border/30 backdrop-blur-md border-t border-dark-border/50 py-3 mt-8">
        <div class="container mx-auto px-4 text-center text-sm text-gray-400">
            <p>数据来源: QWeather &copy; 2025 台风监测系统</p>
        </div>
    </footer>

    <script>
        // API 配置
        const API_CONFIG = {
            stormListUrl: 'https://devapi.qweather.com/v7/tropical/storm-list',
            stormForecastUrl: 'https://devapi.qweather.com/v7/tropical/storm-forecast',
            key: '005ad0bad9f442e6ac4df7d29a73ffa5',
            basin: 'NP',
            year: '2025'
        };

        // 台风等级映射
        const TYPHOON_TYPE_MAPPING = {
            "TD": "热带低压",
            "TS": "热带风暴",
            "STS": "强热带风暴",
            "TY": "台风",
            "STY": "强台风",
            "SuperTY": "超强台风"
        };

        // 当前选中的台风ID
        let selectedStormId = null;
        // 保存图表实例，用于销毁
        let typhoonPathChartInstance = null;

        // 安全设置元素文本内容的函数
        function safeSetTextContent(elementId, text) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = text;
                return true;
            }
            console.warn(`元素ID不存在: ${elementId}`);
            return false;
        }

        // 更新当前时间
        function updateCurrentTime() {
            const now = new Date();
            const options = { 
                year: 'numeric', 
                month: '2-digit', 
                day: '2-digit', 
                hour: '2-digit', 
                minute: '2-digit', 
                second: '2-digit',
                hour12: false,
                timeZone: 'Asia/Shanghai'
            };
            safeSetTextContent('current-time', now.toLocaleString('zh-CN', options));
        }

        // 初始化图表
        function initCharts() {
            // 温度图表
            new Chart(document.getElementById('temperature-chart'), {
                type: 'line',
                data: {
                    labels: ['', '', '', '', '', '', ''],
                    datasets: [{
                        data: [25.2, 25.8, 26.1, 26.3, 26.5, 26.4, 26.5],
                        borderColor: '#FF7D00',
                        backgroundColor: 'rgba(255, 125, 0, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: { display: false },
                        y: { display: false, min: 24, max: 28 }
                    }
                }
            });

            // 湿度图表
            new Chart(document.getElementById('humidity-chart'), {
                type: 'line',
                data: {
                    labels: ['', '', '', '', '', '', ''],
                    datasets: [{
                        data: [72, 74, 76, 75, 77, 78, 78],
                        borderColor: '#36BFFA',
                        backgroundColor: 'rgba(54, 191, 250, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: { display: false },
                        y: { display: false, min: 65, max: 85 }
                    }
                }
            });

            // 风速图表
            new Chart(document.getElementById('wind-speed-chart'), {
                type: 'line',
                data: {
                    labels: ['', '', '', '', '', '', ''],
                    datasets: [{
                        data: [15.2, 16.5, 17.8, 18.2, 18.5, 18.3, 18.2],
                        borderColor: '#165DFF',
                        backgroundColor: 'rgba(22, 93, 255, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: { display: false },
                        y: { display: false, min: 12, max: 22 }
                    }
                }
            });

            // 气压图表
            new Chart(document.getElementById('pressure-chart'), {
                type: 'line',
                data: {
                    labels: ['', '', '', '', '', '', ''],
                    datasets: [{
                        data: [1004.2, 1003.8, 1003.5, 1003.0, 1002.5, 1002.2, 1002.0],
                        borderColor: '#A855F7',
                        backgroundColor: 'rgba(168, 85, 247, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: { display: false },
                        y: { display: false, min: 1000, max: 1006 }
                    }
                }
            });

            // 降雨量图表
            new Chart(document.getElementById('rainfall-chart'), {
                type: 'bar',
                data: {
                    labels: ['', '', '', '', '', '', ''],
                    datasets: [{
                        data: [0, 2.5, 5.3, 8.2, 12.5, 18.7, 25.8],
                        backgroundColor: '#3B82F6',
                        borderRadius: 4,
                        barPercentage: 0.6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: { display: false },
                        y: { display: false, min: 0, max: 30 }
                    }
                }
            });
        }

        // 获取台风列表
        async function fetchStormList() {
            try {
                const response = await fetch(`${API_CONFIG.stormListUrl}?basin=${API_CONFIG.basin}&year=${API_CONFIG.year}&key=${API_CONFIG.key}`);
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                const data = await response.json();
                
                if (data.code === "200") {
                    // 更新更新时间
                    safeSetTextContent('update-time', `上次更新: ${formatDateTime(data.updateTime)}`);
                    
                    // 过滤活跃台风
                    const activeStorms = data.storm.filter(storm => storm.isActive === "1");
                    safeSetTextContent('active-count', activeStorms.length);
                    
                    // 渲染台风列表
                    renderStormList(activeStorms);
                    
                    // 如果有活跃台风，默认选中第一个
                    if (activeStorms.length > 0) {
                        selectStorm(activeStorms[0].id);
                    }
                } else {
                    showError(`获取台风列表失败: ${data.code}`);
                }
            } catch (error) {
                showError(`获取台风列表失败: ${error.message}`);
            }
        }

        // 获取台风预测数据
        async function fetchStormForecast(stormId) {
            try {
                document.getElementById('chart-loading')?.classList.remove('hidden');
                
                const response = await fetch(`${API_CONFIG.stormForecastUrl}?stormid=${stormId}&key=${API_CONFIG.key}`);
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                const data = await response.json();
                
                if (data.code === "200") {
                    // 更新更新时间
                    safeSetTextContent('update-time', `上次更新: ${formatDateTime(data.updateTime)}`);
                    
                    // 渲染台风详情
                    renderStormDetails(data.forecast);
                    
                    // 渲染台风路径图
                    renderTyphoonPathChart(data.forecast);
                } else {
                    showError(`获取台风详情失败: ${data.code}`);
                }
            } catch (error) {
                showError(`获取台风详情失败: ${error.message}`);
            } finally {
                document.getElementById('chart-loading')?.classList.add('hidden');
            }
        }

        // 渲染台风列表 - 水平滚动样式
        function renderStormList(storms) {
            const typhoonListEl = document.getElementById('typhoon-list');
            if (!typhoonListEl) {
                console.error('台风列表容器元素不存在');
                return;
            }
            
            if (storms.length === 0) {
                typhoonListEl.innerHTML = `
                    <div class="flex items-center justify-center h-8 text-gray-400 flex-1">
                        <i class="fa fa-info-circle mr-2"></i>暂无活跃台风
                    </div>
                `;
                document.getElementById('typhoon-details')?.classList.add('hidden');
                return;
            }
            
            // 水平滚动的台风列表项
            typhoonListEl.innerHTML = storms.map(storm => `
                <div class="typhoon-item ${storm.id === selectedStormId ? 'typhoon-active' : 'border-l-4 border-transparent hover:bg-dark-card/50 transition-all duration-300'}" data-id="${storm.id}">
                    <div class="flex flex-col p-2 cursor-pointer min-w-[120px]">
                        <div class="font-medium text-center">${storm.name}</div>
                        <div class="text-gray-400 text-xs text-center">${storm.id}</div>
                    </div>
                </div>
            `).join('');
            
            // 添加点击事件
            document.querySelectorAll('.typhoon-item').forEach(item => {
                item.addEventListener('click', () => {
                    const stormId = item.getAttribute('data-id');
                    selectStorm(stormId);
                });
            });
        }

        // 选择台风
        function selectStorm(stormId) {
            if (selectedStormId === stormId) return;
            
            selectedStormId = stormId;
            
            // 更新台风列表选中状态
            document.querySelectorAll('.typhoon-item').forEach(item => {
                if (item.getAttribute('data-id') === stormId) {
                    item.classList.add('typhoon-active');
                } else {
                    item.classList.remove('typhoon-active');
                }
            });
            
            // 获取台风详情面板
            const detailsPanel = document.getElementById('typhoon-details');
            if (!detailsPanel) {
                console.error('台风详情面板元素不存在');
                return;
            }
            
            // 获取台风详情
            const selectedStorm = document.querySelector(`.typhoon-item[data-id="${stormId}"]`);
            if (selectedStorm) {
                const stormName = selectedStorm.querySelector('.font-medium')?.textContent || '未知台风';
                safeSetTextContent('selected-typhoon-name', stormName);
                safeSetTextContent('selected-typhoon-id', stormId);
                detailsPanel.classList.remove('hidden');
                
                fetchStormForecast(stormId);
            } else {
                showError('未找到选中的台风信息');
            }
        }

        // 渲染台风详情
        function renderStormDetails(forecast) {
            // 检查台风详情面板是否存在且可见
            const detailsPanel = document.getElementById('typhoon-details');
            if (!detailsPanel || detailsPanel.classList.contains('hidden')) {
                return;
            }
            
            if (!forecast || forecast.length === 0) {
                // 重置所有详情字段为默认值
                safeSetTextContent('current-location', '--');
                safeSetTextContent('current-pressure', '-- hPa');
                safeSetTextContent('current-wind-speed', '-- m/s');
                safeSetTextContent('typhoon-type', '--');
                
                safeSetTextContent('forecast-12h-location', '--');
                safeSetTextContent('forecast-12h-wind', '--');
                
                safeSetTextContent('forecast-24h-location', '--');
                safeSetTextContent('forecast-24h-wind', '--');
                
                safeSetTextContent('forecast-48h-location', '--');
                safeSetTextContent('forecast-48h-wind', '--');
                
                return;
            }
            
            try {
                const current = forecast[0];
                
                // 更新当前数据
                safeSetTextContent('current-location', current.lat && current.lon 
                    ? `${current.lat}°N, ${current.lon}°E` 
                    : '--');
                    
                safeSetTextContent('current-pressure', current.pressure 
                    ? `${current.pressure} hPa` 
                    : '-- hPa');
                    
                safeSetTextContent('current-wind-speed', current.windSpeed 
                    ? `${current.windSpeed} m/s` 
                    : '-- m/s');
                    
                safeSetTextContent('typhoon-type', current.type 
                    ? (TYPHOON_TYPE_MAPPING[current.type] || current.type) 
                    : '--');
                
                // 更新预测数据 - 12小时后
                if (forecast.length > 1) {
                    const forecast12h = forecast[1];
                    safeSetTextContent('forecast-12h-location', forecast12h.lat && forecast12h.lon
                        ? `${forecast12h.lat}°N, ${forecast12h.lon}°E`
                        : '--');
                    safeSetTextContent('forecast-12h-wind', forecast12h.windSpeed
                        ? `${forecast12h.windSpeed}`
                        : '--');
                } else {
                    safeSetTextContent('forecast-12h-location', '数据不足');
                    safeSetTextContent('forecast-12h-wind', '--');
                }
                
                // 更新预测数据 - 24小时后
                if (forecast.length > 2) {
                    const forecast24h = forecast[2];
                    safeSetTextContent('forecast-24h-location', forecast24h.lat && forecast24h.lon
                        ? `${forecast24h.lat}°N, ${forecast24h.lon}°E`
                        : '--');
                    safeSetTextContent('forecast-24h-wind', forecast24h.windSpeed
                        ? `${forecast24h.windSpeed}`
                        : '--');
                } else {
                    safeSetTextContent('forecast-24h-location', '数据不足');
                    safeSetTextContent('forecast-24h-wind', '--');
                }
                
                // 更新预测数据 - 48小时后
                if (forecast.length > 4) {
                    const forecast48h = forecast[4];
                    safeSetTextContent('forecast-48h-location', forecast48h.lat && forecast48h.lon
                        ? `${forecast48h.lat}°N, ${forecast48h.lon}°E`
                        : '--');
                    safeSetTextContent('forecast-48h-wind', forecast48h.windSpeed
                        ? `${forecast48h.windSpeed}`
                        : '--');
                } else {
                    safeSetTextContent('forecast-48h-location', '数据不足');
                    safeSetTextContent('forecast-48h-wind', '--');
                }
            } catch (error) {
                showError(`渲染台风详情时出错: ${error.message}`);
                console.error('渲染台风详情错误:', error);
            }
        }

        // 渲染台风路径图
        function renderTyphoonPathChart(forecast) {
            try {
                // 先销毁已存在的图表实例
                if (typhoonPathChartInstance) {
                    typhoonPathChartInstance.destroy();
                }
                
                const ctx = document.getElementById('typhoon-path-chart')?.getContext('2d');
                if (!ctx) {
                    console.error('台风路径图表画布元素不存在');
                    return;
                }
                
                if (!forecast || forecast.length === 0) {
                    console.warn('没有可用的台风路径数据');
                    return;
                }
                
                // 提取经纬度数据
                const lats = forecast.map(point => parseFloat(point.lat) || 0);
                const lons = forecast.map(point => parseFloat(point.lon) || 0);
                
                // 计算经纬度范围
                const minLat = Math.min(...lats) - 1;
                const maxLat = Math.max(...lats) + 1;
                const minLon = Math.min(...lons) - 1;
                const maxLon = Math.max(...lons) + 1;
                
                // 创建图表并保存实例
                typhoonPathChartInstance = new Chart(ctx, {
                    type: 'scatter',
                    data: {
                        datasets: [
                            {
                                label: '台风路径',
                                data: forecast.map((point, index) => ({
                                    x: parseFloat(point.lon) || 0,
                                    y: parseFloat(point.lat) || 0
                                })),
                                backgroundColor: (context) => {
                                    const index = context.dataIndex;
                                    if (index === 0) return 'rgba(245, 63, 63, 1)'; // 当前位置为红色
                                    return 'rgba(22, 93, 255, 0.8)'; // 其他位置为蓝色
                                },
                                borderColor: 'rgba(255, 255, 255, 0.8)',
                                borderWidth: 1,
                                pointRadius: (context) => {
                                    const index = context.dataIndex;
                                    if (index === 0) return 8; // 当前位置大点
                                    return 5;
                                },
                                pointHoverRadius: 8,
                                showLine: true,
                                borderWidth: 2,
                                borderColor: 'rgba(22, 93, 255, 0.8)',
                                tension: 0.2
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        interaction: {
                            mode: 'nearest',
                            intersect: false
                        },
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: (context) => {
                                        const point = forecast[context.dataIndex];
                                        if (!point) return [];
                                        
                                        const date = new Date(point.fxTime || '');
                                        const timeStr = date.toString() !== 'Invalid Date' 
                                            ? `${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:00`
                                            : '时间未知';
                                        
                                        return [
                                            `${timeStr}`,
                                            `位置: ${point.lat || '--'}°N, ${point.lon || '--'}°E`,
                                            `气压: ${point.pressure || '--'} hPa`,
                                            `风速: ${point.windSpeed || '--'} m/s`,
                                            `等级: ${point.type ? (TYPHOON_TYPE_MAPPING[point.type] || point.type) : '--'}`
                                        ];
                                    }
                                }
                            }
                        },
                        scales: {
                            x: {
                                type: 'linear',
                                position: 'bottom',
                                title: {
                                    display: true,
                                    text: '经度(°E)',
                                    color: 'rgba(255, 255, 255, 0.7)'
                                },
                                min: minLon,
                                max: maxLon,
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                },
                                ticks: {
                                    color: 'rgba(255, 255, 255, 0.7)'
                                }
                            },
                            y: {
                                type: 'linear',
                                position: 'left',
                                title: {
                                    display: true,
                                    text: '纬度(°N)',
                                    color: 'rgba(255, 255, 255, 0.7)'
                                },
                                min: minLat,
                                max: maxLat,
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                },
                                ticks: {
                                    color: 'rgba(255, 255, 255, 0.7)'
                                }
                            }
                        }
                    }
                });
            } catch (error) {
                showError(`渲染台风路径图时出错: ${error.message}`);
                console.error('渲染台风路径图错误:', error);
            }
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '未知';
            
            try {
                const date = new Date(dateTimeStr);
                if (date.toString() === 'Invalid Date') return dateTimeStr;
                
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                });
            } catch (error) {
                return dateTimeStr;
            }
        }

        // 显示错误信息
        function showError(message) {
            console.error(message);
            // 创建自定义错误提示元素
            const errorDiv = document.createElement('div');
            errorDiv.className = 'fixed top-4 right-4 bg-danger text-white px-4 py-3 rounded-lg shadow-lg z-50 flex items-center';
            errorDiv.innerHTML = `
                <i class="fa fa-exclamation-circle mr-2"></i>
                <span>${message}</span>
                <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.remove()">
                    <i class="fa fa-times"></i>
                </button>
            `;
            document.body.appendChild(errorDiv);
            
            // 3秒后自动关闭
            setTimeout(() => {
                errorDiv.classList.add('opacity-0', 'transition-opacity', 'duration-300');
                setTimeout(() => errorDiv.remove(), 300);
            }, 3000);
        }

        // 模拟设备数据
        function simulateDeviceData() {
            // 随机风速方向 (0-360度)
            const windDirection = Math.floor(Math.random() * 360);
            const indicator = document.querySelector('#wind-direction-indicator div');
            if (indicator) {
                indicator.style.transform = `rotate(${windDirection}deg) origin-bottom`;
            }
            
            // 更新风向文本
            const windDirections = ['北', '东北偏北', '东北', '东北偏东', '东', '东南偏东', '东南', '东南偏南', '南', '西南偏南', '西南', '西南偏西', '西', '西北偏西', '西北', '西北偏北'];
            const directionIndex = Math.floor((windDirection % 360) / 22.5);
            safeSetTextContent('wind-direction-text', windDirections[directionIndex] || '未知');
            safeSetTextContent('wind-direction-deg', `${windDirection}°`);
            
            // 随机更新其他设备数据
            const temperature = (25 + Math.random() * 3).toFixed(1);
            safeSetTextContent('temperature-value', `${temperature}°C`);
            
            const humidity = Math.floor(70 + Math.random() * 15);
            safeSetTextContent('humidity-value', `${humidity}%`);
            
            const windSpeed = (15 + Math.random() * 8).toFixed(1);
            safeSetTextContent('wind-speed-value', `${windSpeed} m/s`);
            
            const pressure = (1000 + Math.random() * 5).toFixed(1);
            safeSetTextContent('pressure-value', `${pressure} hPa`);
            
            const rainfall = (20 + Math.random() * 15).toFixed(1);
            safeSetTextContent('rainfall-value', `${rainfall} mm`);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 更新当前时间
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);
            
            // 初始化图表
            initCharts();
            
            // 获取台风列表
            fetchStormList();
            
            // 模拟设备数据更新
            simulateDeviceData();
            setInterval(simulateDeviceData, 5000);
            
            // 刷新按钮事件
            const refreshBtn = document.getElementById('refresh-btn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', () => {
                    if (selectedStormId) {
                        fetchStormForecast(selectedStormId);
                    } else {
                        fetchStormList();
                    }
                });
            }
            
            // 全屏按钮事件
            const fullscreenBtn = document.getElementById('fullscreen-btn');
            if (fullscreenBtn) {
                fullscreenBtn.addEventListener('click', () => {
                    const elem = document.documentElement;
                    if (!document.fullscreenElement) {
                        elem.requestFullscreen().catch(err => {
                            showError(`全屏请求失败: ${err.message}`);
                        });
                    } else {
                        document.exitFullscreen();
                    }
                });
            }
        });
    </script>
</body>
</html>
    